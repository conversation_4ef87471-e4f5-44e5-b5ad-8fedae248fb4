; C 语言 Tree-sitter 查询模式
; Tree-sitter 解析器支持的 C 语言构造：
;
; 1. 类似类的构造：
; - 结构体定义（带字段）
; - 联合体定义（带变体）
; - 枚举定义（带值）
; - 匿名联合体/结构体
; - 对齐结构体
;
; 2. 函数相关构造：
; - 函数定义（带参数）
; - 函数声明（原型）
; - 静态函数
; - 函数指针
;
; 3. 类型定义：
; - typedef 声明（所有类型）
; - 函数指针 typedef
; - 结构体/联合体 typedef
;
; 4. 变量声明：
; - 全局变量
; - 静态变量
; - 数组声明
; - 指针声明
;
; 5. 预处理器构造：
; - 函数式宏
; - 对象式宏
; - 条件编译

; 函数定义和声明
; 匹配样例: int main() { return 0; }
(function_definition
  declarator: (function_declarator
    declarator: (identifier) @name.definition.function))

; 函数声明（原型）
; 匹配样例: int add(int a, int b);
(declaration
  type: (_)?
  declarator: (function_declarator
    declarator: (identifier) @name.definition.function
    parameters: (parameter_list)?)?) @definition.function

; 函数声明器
; 匹配样例: int (*func_ptr)(int, int)
(function_declarator
  declarator: (identifier) @name.definition.function
  parameters: (parameter_list)?) @definition.function

; 结构体定义
; 匹配样例: struct Point { int x, y; };
(struct_specifier
  name: (type_identifier) @name.definition.struct) @definition.struct

; 联合体定义
; 匹配样例: union Data { int i; float f; };
(union_specifier
  name: (type_identifier) @name.definition.union) @definition.union

; 枚举定义
; 匹配样例: enum Color { RED, GREEN, BLUE };
(enum_specifier
  name: (type_identifier) @name.definition.enum) @definition.enum

; Typedef 声明
; 匹配样例: typedef int MyInt;
(type_definition
  declarator: (type_identifier) @name.definition.type) @definition.type

; 全局变量
; 匹配样例: int global_var = 10;
(declaration
  (storage_class_specifier)?
  type: (_)
  declarator: (identifier) @name.definition.variable) @definition.variable

; 带初始化的变量声明
; 匹配样例: static int count = 0;
(declaration
  (storage_class_specifier)?
  type: (_)
  declarator: (init_declarator
    declarator: (identifier) @name.definition.variable)) @definition.variable

; 对象式宏
; 匹配样例: #define PI 3.14159
(preproc_def
  name: (identifier) @name.definition.macro) @definition.macro

; 函数式宏
; 匹配样例: #define MAX(a, b) ((a) > (b) ? (a) : (b))
(preproc_function_def
  name: (identifier) @name.definition.macro) @definition.macro
