; C++ Tree-sitter 查询模式
; 支持的 C++ 结构：
; - struct/class/union 声明
; - 函数/方法声明
; - typedef 声明
; - enum 声明
; - namespace 定义
; - template 声明
; - 宏定义
; - 变量声明
; - 构造函数/析构函数
; - 运算符重载
; - friend 声明
; - using 声明

; 结构体声明
; 匹配样例: struct MyStruct { int x; };
(struct_specifier
  name: (type_identifier) @name.definition.class) @definition.class

; 联合体声明
; 匹配样例: union MyUnion { int x; float y; };
(union_specifier
  name: (type_identifier) @name.definition.class) @definition.class

; 函数声明（原型）
; 匹配样例: int add(int a, int b);
(declaration
  type: (_)
  declarator: (function_declarator
    declarator: (identifier) @name.definition.function)) @definition.function

; 函数定义（带函数体）
; 匹配样例: int add(int a, int b) { return a + b; }
(function_definition
  type: (_)
  declarator: (function_declarator
    declarator: (identifier) @name.definition.function)) @definition.function

; 方法定义（类成员函数）
; 匹配样例: class MyClass { void method() { } };
(function_definition
  declarator: (function_declarator
    declarator: (field_identifier) @name.definition.method)) @definition.method

; 类型定义
; 匹配样例: typedef int MyInt;
(type_definition
  type: (_)
  declarator: (type_identifier) @name.definition.type) @definition.type

; 类声明
; 匹配样例: class MyClass { public: int x; };
(class_specifier
  name: (type_identifier) @name.definition.class) @definition.class

; 枚举声明
; 匹配样例: enum Color { RED, GREEN, BLUE };
(enum_specifier
  name: (type_identifier) @name.definition.enum) @definition.enum

; 命名空间定义
; 匹配样例: namespace MyNamespace { class MyClass {}; }
(namespace_definition
  name: (namespace_identifier) @name.definition.namespace) @definition.namespace

; 嵌套命名空间定义
; 匹配样例: namespace Outer { namespace Inner { } }
(namespace_definition
  body: (declaration_list
    (namespace_definition
      name: (namespace_identifier) @name.definition.namespace))) @definition.namespace

; 模板声明
; 匹配样例: template<typename T> class MyTemplate { T data; };
(template_declaration
  parameters: (template_parameter_list)
  (class_specifier
    name: (type_identifier) @name.definition.template.class)) @definition.template

; 宏定义
; 匹配样例: #define MAX(a, b) ((a) > (b) ? (a) : (b))
(preproc_function_def
  name: (identifier) @name.definition.macro) @definition.macro

; 变量声明（带初始化）
; 匹配样例: int x = 10;
(declaration
  type: (_)
  declarator: (init_declarator
    declarator: (identifier) @name.definition.variable)) @definition.variable

; 构造函数声明
; 匹配样例: class MyClass { MyClass() { } };
(function_definition
  declarator: (function_declarator
    declarator: (identifier) @name.definition.constructor)) @definition.constructor

; 析构函数声明
; 匹配样例: class MyClass { ~MyClass() { } };
(function_definition
  declarator: (function_declarator
    declarator: (destructor_name) @name.definition.destructor)) @definition.destructor

; 运算符重载
; 匹配样例: class MyClass { MyClass operator+(const MyClass& other) { } };
(function_definition
  declarator: (function_declarator
    declarator: (operator_name) @name.definition.operator)) @definition.operator

; 友元声明
; 匹配样例: class MyClass { friend class OtherClass; };
(friend_declaration) @definition.friend

; using 声明
; 匹配样例: using std::cout;
(using_declaration) @definition.using
