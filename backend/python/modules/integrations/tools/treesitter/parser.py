from pathlib import Path
from typing import Dict, List, Optional, Tuple
from tree_sitter import Node, Parse<PERSON>, Query, QueryCursor
from tree_sitter_language_pack import get_parser

from core.config import ChunkConfig
from modules.common.constants import LanguageEnum
from modules.common.schema import Chunk
from utils.trace_logger import get_trace_logger


trace_logger = get_trace_logger(__name__)

class FileParser:
    """Manages tree-sitter parsers for different languages."""
    def __init__(self, chunk_config: ChunkConfig):
        self.language_parsers: Dict[LanguageEnum, Parser] = {}
        self.language_queries: Dict[LanguageEnum, QueryCursor] = {}
        self.config = chunk_config

    def parse(self, file_path: str, file_content: str) -> Tuple[Dict[int, str], List[Chunk]]:
        # 根据文件后缀名选择对应的解析器
        language_enum = LanguageEnum.from_suffix(file_path.split('.')[-1])
        
        if language_enum is None:
            return None
        
        # 加载parser
        parser = None
        if language_enum in self.language_parsers:
            parser = self.language_parsers[language_enum]
        else:
            try:
                parser = get_parser(language_enum.value[0])
                self.language_parsers[language_enum] = parser
            except Exception as e:
                self.language_parsers[language_enum] = None
                trace_logger.info(f"Failed to load parser for {language_enum}: {e}")
                return None

        # 加载query
        query_cursor = None
        if language_enum in self.language_queries:
            query_cursor = self.language_queries[language_enum]
        else:
            query_content = self._load_query(language_enum)
            if not query_content:
                self.language_queries[language_enum] = None
                trace_logger.info(f"Failed to load query for {language_enum}")
                return None
            
            try:
                query_cursor = QueryCursor(Query(parser.language, query_content))
                self.language_queries[language_enum] = query_cursor
            except Exception as e:
                self.language_queries[language_enum] = None
                trace_logger.info(f"Failed to load query for {language_enum}: {e}")
                return None

        if parser is None or query_cursor is None:
            return None
        
        # 解析类
        tree = parser.parse(file_content.encode("utf-8"))
        captures_dict = query_cursor.captures(tree.root_node)
        
        # 解析文件结构
        definitions: List[Tuple[Node, str]] = []
        processed_nodes = set()
        for capture_name, nodes in captures_dict.items():
            if "definition" in capture_name:
                for node in nodes:
                    node_key = (node.start_point, node.end_point)
                    if node_key not in processed_nodes:
                        definitions.append((node, capture_name))
                        processed_nodes.add(node_key)

        definitions.sort(key=lambda x: x[0].start_point[0])

        lines = file_content.splitlines()
        key_structure_lines: Dict[int, str] = {}
        file_chunks = []
        
        prev_nodes: List[Node] = []
        for node, capture_name in definitions:
            if self._is_keynode(node, capture_name):
                key_structure_lines[node.start_point[0]] = capture_name
            
            node_line_cnt = node.end_point[0] - node.start_point[0] + 1
            
            # 如果当前chunk组装过程中没有内容，且当前节点不足以组成一个chunk，则跳过
            if not prev_nodes and node_line_cnt < self.config.min_chunk_size:
                prev_nodes.append(node)
                continue

            # 如果当前节点与上一个节点之间有间隔，则将间隔部分作为一个chunk
            if prev_nodes and node.end_point[0] - prev_nodes[0].start_point[0] >= self.config.min_chunk_size:
                if node.end_point[0] - prev_nodes[0].start_point[0] < self.config.max_chunk_size:
                    file_chunks.append(
                        Chunk(
                            file_path=file_path,
                            start_line=prev_nodes[0].start_point[0],
                            end_line=node.end_point[0],
                            content="\n".join(lines[prev_nodes[0].start_point[0]: node.end_point[0] + 1]),
                        )
                    )
                    prev_nodes = []
                else:
                    file_chunks.append(
                        Chunk(
                            file_path=file_path,
                            start_line=prev_nodes[0].start_point[0],
                            end_line=prev_nodes[-1].end_point[0],
                            content="\n".join(lines[prev_nodes[0].start_point[0]: prev_nodes[-1].end_point[0] + 1]),
                        )
                    )
                    prev_nodes = [node]

        if prev_nodes:
            file_chunks.append(
                Chunk(
                    file_path=file_path,
                    start_line=prev_nodes[0].start_point[0],
                    end_line=prev_nodes[-1].end_point[0],
                    content="\n".join(lines[prev_nodes[0].start_point[0]: prev_nodes[-1].end_point[0] + 1]),
                )
            )
        
        return key_structure_lines, file_chunks
    
    def _load_query(self, language_enum: LanguageEnum) -> Optional[str]:
        """Load query string for a language."""
        # Try to load from queries directory
        query_file = Path(__file__).parent / "queries" / f"{language_enum.value[0]}.scm"
        if query_file.exists():
            try:
                return query_file.read_text(encoding="utf-8")
            except Exception as e:
                trace_logger.warning(f"读取查询文件 {query_file} 失败: {e}")

        return None
    
    def _is_keynode(self, node: Node, capture_name: str) -> bool:
        """判断当前节点是否为关键节点，并返回对应的EntryType和名称"""
        name = None

        # Extract name from node
        if hasattr(node, "child_by_field_name"):
            name_node = node.child_by_field_name("name")
            if name_node:
                name = name_node.text.decode("utf-8")

        # Determine entry type based on capture name
        if capture_name.lower() in ["class", "interface", "struct", "type"]:
            return True
        
        return False

