from typing import Dict
from .constants import LanguageEnum

class FileParser:
    """Manages tree-sitter parsers for different languages."""
    def __init__(self):
        self.language_parsers: Dict[LanguageEnum, any] = {}

    @staticmethod
    def parse(file_path: str, file_content: str):
        # 根据文件后缀名选择对应的解析器
        
        # 解析文件依赖

        # 解析类

        # 解析函数签名

        # return 文件结构, 文件图谱
        
        pass