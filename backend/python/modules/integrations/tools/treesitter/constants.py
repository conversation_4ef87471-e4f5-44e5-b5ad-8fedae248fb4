from enum import Enum

class LanguageEnum(Enum):
    JAVA = ".java"
    PYTHON = ".py"
    TEXT = ".txt"
    C = ".c"
    CPP = ".cpp"
    GO = ".go"
    RUST = ".rs"
    PHP = ".php"
    RUBY = ".rb"
    SWIFT = ".swift"
    KOTLIN = ".kt"
    SCALA = ".scala"
    HTML = ".html"
    CSS = ".css"
    JAVASCRIPT = ".js"
    TYPESCRIPT = ".ts"
    JSX = ".jsx"
    TSX = ".tsx"
    JSON = ".json"
    XML = ".xml"
    YAML = ".yaml"
    YML = ".yml"
    TOML = ".toml"
    MD = ".md"
    SQL = ".sql"
    SH = ".sh"
    BASH = ".bash"
    ZSH = ".zsh"
    FISH = ".fish"
    PS1 = ".ps1"
    DOCKERFILE = ".dockerfile"
    MAKEFILE = ".makefile"

    @classmethod
    def from_suffix(cls, suffix: str):
        for lang in cls:
            if lang.value == suffix:
                return lang
        return cls.TEXT
