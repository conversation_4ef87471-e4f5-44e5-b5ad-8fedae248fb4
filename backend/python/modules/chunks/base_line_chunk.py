from typing import Dict
from modules.chunks.IChunk import IChunk
from modules.common.constant import SuffixLanguage


class BaseLineChunk(IChunk):
    def chunk_file(self, file_content: str, language: SuffixLanguage = SuffixLanguage.TEXT, window_size: int = 50, overflow_size: int = 10) -> Dict[str, str]:
        # 移除代码中的空白行
        lines = file_content.split('\n')
        
        chunks = {}
        # 按照windo_size和overflow_size进行分块
        for i in range(0, len(lines), window_size - overflow_size):
            chunks.update(
                {
                    f"{i + 1}-{i + window_size + 1}": "\n".join(lines[i : i + window_size]) # 行号范围从1开始
                }
            )
            
        return chunks
    