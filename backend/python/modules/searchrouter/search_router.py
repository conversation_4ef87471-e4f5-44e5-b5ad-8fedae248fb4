# 1. 用户的输入问题
# 2. 问题发散并决定调用工具
# 3. 并发调用 ToolCall
    # 3.1 EmbeddingSearch (input: new query, repo basic info, output: code snippets )
        # 3.1.1 问题重写 -> 查询的代码/文档的描述性文字
        # 3.1.2 是否被Emedding过？没有则对当前仓库进行Embedding
        # 3.1.3 调用远程Embedding服务进行搜索
        # 3.1.4 代码片段去重
    # 3.2 Term-Parse Search (input: query, output: code snippets )
        # 3.2.1 问题重写 -> 重写为可能出现在文档中的描述或者代码
        # 3.2.2 是否有本地缓存信息？有则加载，无则对当前仓库进行索引
        # 3.2.3 代码片段去重
    # 3.3 GrepSearch ( input: grep keywords, output: code snippets )
        # 3.3.1 问题重写 -> 重写为可被grep搜索到的关键词
        # 3.3.2 执行grep搜索
        # 3.3.3 代码片段去重
    # 3.4 ReadFile( input: file path, output: file content )
        # 3.4.1 读取文件内容
    # 3.5 ReadDir( input: dir path, output: file list )
        # 3.5.1 读取目录内容
    # 3.6 GraphSerch( input: node, search type, search depth, output: code snippets )
        # 3.6.1 问题重写 -> 重写为可被图搜索理解的查询
        # 3.6.2 是否有本地图信息？有则加载，无则对当前仓库进行图索引
        # 3.6.3 代码片段去重
# 4. 汇总结果
# 5. 判断是否符合需要

import re
from typing import List, Any
from modules.llm.llm_client import LLMClient, default_llm_client
from utils.file import FileNode, build_file_tree
from utils.trace_logger import get_trace_logger
from core.config import get_config
from modules.common.constant import FileFilterMode, IOToolEnum, SearchToolEnum
from modules.integrations.tools.search.any_search import get_search_tool_instance
from modules.integrations.tools.io.any_io import AnyIOTool
from modules.common.schema import CodeSnippet, ContextOperation, ContextOperationResult, SearchResult, SearchQuery
from modules.searchrouter.prompts import GENERATE_NEW_QUERY_PROMPT, READ_CONTEXT_PROMPT, SNIPPETS_REORDER_PROMPT, SYSTEM_PROMPTS


trace_logger = get_trace_logger(__name__)

class SearchRouter:
    def __init__(
        self,
        repo_path: str,
        repo_info: str = "",
        llm_client: LLMClient = None,
        search_tool: SearchToolEnum = SearchToolEnum.ANY
    ):
        self.side_memory: List[Any]  = [] # 旁路记忆，可以用于暂存从IDE中获取的信息

        self.repo_info = repo_info # 仓库信息
        self.repo_path = repo_path # 搜索目标路径
        
        self.search_tool = search_tool # 搜索工具类型

        self.search_instance = get_search_tool_instance(
            SearchToolEnum.ANY, # 使用ANY类型的Search工具来中转对其他工具的调用，避免直接初始化对应的search工具，通过这种方式统一工具调用的输入入口
            repo_path, 
            enabled_search_tools=[search_tool.value] if search_tool != SearchToolEnum.ANY else get_config().deepsearch.enabled_search_tools) # 可使用的搜索工具，默认是ANY，此时按照配置文件中的设置来确定可用的搜索工具
        self.io_instance = AnyIOTool(repo_path=repo_path)

        self.llm_client = llm_client or default_llm_client

        self.config = get_config().deepsearch

    async def search(self, query: str) -> SearchResult:
        result = SearchResult(original_query=query)

        # 1. 读取所需要的信息，循环MAX_READ_ITERATION次（default=1）
        # 获取当前仓库树结构
        repo_node = FileNode(path=self.repo_path, 
                             name=self.repo_path.split("/")[-1], 
                             type="directory", 
                             children=build_file_tree(root_dir=self.repo_path, start_dir=self.repo_path, max_leaf_nodes=300, filter_mode=FileFilterMode.LOCAL))
        
        sub_queries = await self._split_queris(query, repo_node)
        
        for sub_query in sub_queries:
            self.side_memory.extend(
                self._execute_read_context_operations(sub_query.context_operations)
            )
        all_queries = [sub_query for sub_query in sub_queries]
        code_snippets = []

        # 2. 拆分查询方向，并选定搜索工具
        for iteration in range(self.config.max_iterations):
            if not sub_queries:
                trace_logger.info("未生成新查询，搜索结束")
                break
            
            # 生成子查询
            new_queries = await self._generate_new_queries(query, all_queries)
            all_queries.extend(new_queries)

            # 3. 并发搜索
            try:
                new_snippets = await self._search_and_filter(context_operations=[query.context_operations for query in new_queries])
            except Exception as e:
                trace_logger.error(f"Iteration {iteration + 1}: Search Failed: {e}")
                break

            if not new_snippets:
                # DeepSearch的方法是预设首先拆分的搜索方向没有错误，如果子查询没有找到相关代码，说明当前搜索方向已经穷尽，可以结束搜索
                trace_logger.warning(f"Iteration {iteration + 1}: Not Found Any Code Snippets")
                break
            
            code_snippets.extend(new_snippets)
            code_snippets = self._merge_snippets(code_snippets)

            result.iterations = iteration + 1

        # 4. 将side_memory中读取过的文件信息加入到code_snippets中参与排序
        for element in self.side_memory:
            if isinstance(element, ContextOperationResult) and element.operation.tool == IOToolEnum.FILE:
                code_snippets.append(
                    CodeSnippet(
                        file_path=element.operation.context_uri,
                        start_line=1,
                        end_line=element.result.count("\n"),
                        content=element.result,
                        context_before="",
                        context_after="",
                        score=0.0
                    )
                )

        # 5. 代码片段去重
        code_snippets = self._merge_snippets(code_snippets)
        code_snippets = self._merge_file_snippets(code_snippets)

        # 6. 重排序
        code_snippets = await self._reorder_snippets(query, code_snippets, all_queries)
        result.code_snippets = code_snippets

        return result

    async def _split_queris(self, query: str, repo_struct: str) -> List[SearchQuery]:
        prompt = READ_CONTEXT_PROMPT.format(
            query=query,
            repo_struct=repo_struct,
            available_tools=self.io_instance.description,
            tools_examples=self.io_instance.examples
        )
        
        try:
            response_text = await self.llm_client.call_async(prompt, SYSTEM_PROMPTS['query_split'], stream=False)
        except Exception as e:
            trace_logger.error(f"Split Query Failed: {e}")
            return []

        return SearchQuery.parse_response_text(response_text)

    def _execute_read_context_operations(self, context_operations: List[ContextOperation]) -> List[ContextOperationResult]:
        if not context_operations:
            return []
        
        operation_results = []
        for operation in context_operations:
            operation_result = self.io_instance.read(operation.xml_content, operation.tool)
            operation_results.append(ContextOperationResult(operation=operation, result=operation_result))
            
        return operation_results
    
    async def _generate_new_queries(self, original_query: str, all_quires: List[SearchQuery]) -> List[SearchQuery]:
        prompt = GENERATE_NEW_QUERY_PROMPT.format(
            question=original_query,
            context_content="\n".join([str(element) for element in self.side_memory]),
            max_new_queries=self.config.max_new_queries,
            tool_description=self.search_instance.description,
            tool_exampls=self.search_instance.examples,
            previous_queries="\n\t- ".join([query.text for query in all_quires])
        )

        respose_text = await self.llm_client.call_async(prompt, SYSTEM_PROMPTS['generate_new_query'], stream=False)

        structure_results = SearchQuery.parse_response_text(respose_text)

        return structure_results[:self.config.max_new_queries]
    

    async def _search_and_filter(self, context_operations: List[ContextOperation]) -> List[str]:
        """
        异步搜索并过滤代码片段

        Args:
            queries: 查询列表
            search_tools: 搜索工具列表

        Returns:
            List[CodeSnippet]: 过滤后的代码片段
        """
        # 并行检索所有子查询
        trace_logger.info(f"Async Searching Tool: {context_operations}")
    
        if not context_operations:
            return []

        # 使用asyncio.gather进行并发搜索
        search_tasks = []
        for operation in context_operations:
            # 使用异步搜索方法
            task = self.search_instance.search_async(query=operation.xml_content, search_tool=operation.tool)
            search_tasks.append(task)

        # 等待所有搜索任务完成
        search_results = []
        for task in search_tasks:
            try:
                snippets = await task
                trace_logger.info(f"Found {len(snippets)} Code Snippets")
                search_results.extend(snippets)
            except Exception as exc:
                trace_logger.info(f"Search Failed: {exc}")
                search_results.extend([])

        # 统计总的代码片段数量
        trace_logger.info(f"Async Found {len(search_tasks)} Code Snippets, Start Filtering...")

        return search_results

    def _merge_snippets(self, snippets: List[CodeSnippet]) -> List[CodeSnippet]:
        """
        去重代码片段（基于文件路径和行号）
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            List[CodeSnippet]: 去重后的代码片段列表
        """
        seen_stack = []
        unique_snippets = []
        
        sorted_snippets = sorted(snippets, key=lambda x: (x.file_path, x.start_line, x.end_line))

        for snippet in sorted_snippets:
            # 使用文件路径和行号作为唯一标识
            key = (snippet.file_path, snippet.start_line, snippet.end_line)
            
            if seen_stack and seen_stack[-1][0] == key[0] and seen_stack[-1][1] <= key[1] <= seen_stack[-1][2]:
                # 更新seen_stack的end_line
                seen_stack[-1][2] = max(seen_stack[-1][2], key[2])
                
                # 如果end_line在前一个snippet后面，则合并snippet内容
                if unique_snippets[-1].end_line >= key[2]:
                    unique_snippets[-1].content += "\n".join(snippet.content.split('\n')[(unique_snippets[-1].end_line - snippet.start_line + 1):])
                    unique_snippets[-1].score += snippet.score # 合并后的score相加

                # 更新end_line
                unique_snippets[-1].end_line = seen_stack[-1][2]
                    
            else:   
                seen_stack.append(list(key)) # 因为元素可能被修改，tuple是不可变元组，因此使用list存入
                unique_snippets.append(snippet)

        return unique_snippets
    
    def _merge_file_snippets(self, snippets: List[CodeSnippet]) -> List[CodeSnippet]:
        """
        按照文件路径合并snippets，如果文件路径相同，则合并为一个snippet
        如果相同文件的snippets之间的行号不连续，补充......指代中间的省略部分

        Args:
            snippets: 代码片段列表

        Returns:
            List[CodeSnippet]: 合并后的代码片段列表
        """
        if not snippets:
            return []

        unique_snippets = self._merge_snippets(snippets)

        file_groups = {}
        for snippet in unique_snippets:
            if snippet.file_path not in file_groups:
                file_groups[snippet.file_path] = []
            file_groups[snippet.file_path].append(snippet)

        merged_snippets = []

        for file_path, file_snippets in file_groups.items():
            # 按起始行号排序
            file_snippets.sort(key=lambda x: x.start_line)

            # 然后合并同一文件的所有片段，处理不连续的情况
            if len(file_snippets) == 1:
                merged_snippets.append(file_snippets[0])
            else:
                # 合并为一个大的片段，中间用省略号连接
                merged_content = "\n......\n".join(snippet.content for snippet in file_snippets)
                merged_startline = file_snippets[0].start_line
                merged_endline = file_snippets[-1].end_line
                
                merged_snippets.append(CodeSnippet(
                    file_path=file_path,
                    start_line=merged_startline,
                    end_line=merged_endline,
                    content=merged_content,
                    context_before="",
                    context_after="",
                    score=sum(snippet.score for snippet in file_snippets)
                ))

        return merged_snippets
    
    async def _reorder_snippets(self, original_query: str, snippets: List[CodeSnippet], all_quires: List[SearchQuery]) -> List[CodeSnippet]:
        """
        重排序代码片段

        Args:
            snippets: 代码片段列表
            original_query: 原始查询
            all_quires: 所有查询列表
            side_memories: 侧边记忆列表

        Returns:
            List[CodeSnippet]: 重排序后的代码片段列表
        """
        if not snippets:
            return []

        combined_query = original_query + "\n\t- " + "\n\t- ".join([query.text for query in all_quires])

        # 构建带索引的代码片段摘要
        code_summary = self._build_file_index_summary(snippets)

        prompt = SNIPPETS_REORDER_PROMPT.format(
            max_code_snippets=20,
            query=combined_query,
            code_snippets=code_summary
        )

        response_text = await self.llm_client.call_async(prompt, SYSTEM_PROMPTS['reorder'], stream=False)
        trace_logger.debug(f"Async Reorder Response: {response_text}")

        # 解析LLM返回的JSON格式结果
        try:
            # 提取JSON内容
            import json

            # 尝试直接解析JSON
            try:
                score_dict = json.loads(response_text.strip().replace("\n", ""))
            except json.JSONDecodeError:
                trace_logger.warning("无法从LLM响应中提取JSON格式的评分结果")
                return snippets  # 返回原始顺序

            # 根据评分重新排序
            indexed_snippets = [(i, snippet) for i, snippet in enumerate(snippets)]

            # 创建评分映射 (将字符串键转换为整数)
            score_mapping = {}
            for key, score in score_dict.items():
                try:
                    index = int(key)
                    if 0 <= index < len(snippets):
                        score_mapping[index] = float(score)
                except (ValueError, TypeError):
                    trace_logger.warning(f"无效的索引或评分: {key}={score}")
                    continue

            # 按评分排序，没有评分的片段放在最后
            def get_score(item):
                index, _ = item
                return score_mapping.get(index, 0.0)
            reordered_snippets = [snippet for _, snippet in sorted(indexed_snippets, key=get_score, reverse=True)]

            return reordered_snippets

        except Exception as e:
            trace_logger.error(f"解析异步重排序结果失败: {e}")
            # 如果解析失败，返回原始片段（限制数量）
            return snippets
    
    def _build_file_index_summary(self, snippets: List[CodeSnippet], snippet_max_line: int = 200) -> str:
        """
        构建文件索引摘要
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            str: 文件索引摘要
        """
        if not snippets:
            return "暂无相关代码片段"
        
        summary = ""
        for file_index, snippet in enumerate(snippets):
            summary += f"{file_index}. {snippet.file_path}: {snippet.start_line}-{snippet.end_line}\n"
            summary += f"  {snippet.content[:snippet_max_line]}\n"
            summary += f"--------------------------------------------\n\n"

        return summary